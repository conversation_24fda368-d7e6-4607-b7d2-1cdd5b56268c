import requests
from bs4 import BeautifulSoup
import csv

def fetch_remoteok(keywords):
    url = "https://remoteok.com/"
    headers = {"User-Agent": "Mozilla/5.0"}
    response = requests.get(url, headers=headers)
    response.raise_for_status()

    soup = BeautifulSoup(response.text, "html.parser")
    jobs = []
    for row in soup.find_all("tr", class_="job"):
        title_elem = row.find("h2", itemprop="title")
        company_elem = row.find("h3", itemprop="name")
        location_elem = row.find("div", class_="location")
        link_elem = row.find("a", class_="preventLink")

        if not title_elem or not company_elem:
            continue

        title = title_elem.text.strip()
        company = company_elem.text.strip()
        location = location_elem.text.strip() if location_elem else "Remote"
        link = "https://remoteok.com" + link_elem["href"] if link_elem else None

        # 关键词过滤
        if any(kw.lower() in title.lower() for kw in keywords):
            jobs.append({
                "title": title,
                "company": company,
                "location": location,
                "link": link,
                "source": "RemoteOK"
            })
    return jobs


def fetch_weworkremotely(keywords):
    url = "https://weworkremotely.com/remote-jobs"
    headers = {"User-Agent": "Mozilla/5.0"}
    response = requests.get(url, headers=headers)
    response.raise_for_status()

    soup = BeautifulSoup(response.text, "html.parser")
    jobs = []
    for section in soup.find_all("section", class_="jobs"):
        for li in section.find_all("li", class_="feature"):
            title_elem = li.find("span", class_="title")
            company_elem = li.find("span", class_="company")
            link_elem = li.find("a", href=True)

            if not title_elem or not company_elem:
                continue

            title = title_elem.text.strip()
            company = company_elem.text.strip()
            link = "https://weworkremotely.com" + link_elem["href"]

            # 关键词过滤
            if any(kw.lower() in title.lower() for kw in keywords):
                jobs.append({
                    "title": title,
                    "company": company,
                    "location": "Remote",
                    "link": link,
                    "source": "WeWorkRemotely"
                })
    return jobs


def crawl_jobs(keywords):
    all_jobs = []
    all_jobs.extend(fetch_remoteok(keywords))
    all_jobs.extend(fetch_weworkremotely(keywords))

    # 保存到CSV
    with open("filtered_jobs.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=["title", "company", "location", "link", "source"])
        writer.writeheader()
        writer.writerows(all_jobs)

    print(f"✅ 爬取完成，共获取 {len(all_jobs)} 条职位，已保存到 filtered_jobs.csv")


if __name__ == "__main__":
    # 设置关键词
    keywords = ["Python", "React", "Frontend", "Django"]
    crawl_jobs(keywords)

